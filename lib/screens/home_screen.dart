import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:healo/common/widgets/kidney_card.dart';
import 'package:healo/common/widgets/liver_cards.dart';
import 'package:healo/common/widgets/menstrual_cycle_cards.dart';
import 'package:healo/common/widgets/thyroid_cards.dart';
import 'package:healo/constants/app_colors.dart';
import 'package:healo/providers/blood_pressure_provider.dart';
import 'package:healo/providers/hba1c_provider.dart';
import 'package:healo/providers/health_provider.dart';
import 'package:healo/route/route_constants.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:healo/providers/user_provider.dart';
import 'package:percent_indicator/percent_indicator.dart';
import 'package:healo/common/utils/size.dart';
import 'package:healo/providers/kidney_provider.dart';
import 'package:healo/providers/thyroid_provider.dart';
import 'package:healo/providers/liver_provider.dart';

import 'package:healo/providers/health_data_loader_provider.dart';
import 'package:healo/providers/water_intake_provider.dart';
import 'package:healo/providers/report_provider.dart';
import 'package:healo/providers/bmi_provider.dart';
import 'package:healo/providers/period_provider.dart';
import 'package:intl/intl.dart';
import 'dart:developer';

class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({super.key});

  @override
  ConsumerState<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen> {
  @override
  void initState() {
    super.initState();
    // Load all health data at once using the health data loader provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      log("HomeScreen: Loading all health data");
      ref.read(healthDataLoaderProvider.notifier).loadAllHealthData();

      // Also force refresh user data to ensure it's up to date
      log("HomeScreen: Invalidating user data providers");
      ref.invalidate(userDataProvider);
    });
  }

  @override
  Widget build(BuildContext context) {
    // Use stream-based provider for real-time updates
    final userName = ref.watch(userNameStreamProvider);

    final heartRate = ref.watch(healthDataProvider).heartRate?.toInt() ?? 00;
    final latestReading = ref.watch(latestBloodPressureReadingProvider);

    return SafeArea(
      child: Scaffold(
          backgroundColor: Theme.of(context).scaffoldBackgroundColor,
          body: RefreshIndicator(
            onRefresh: () async {
              await ref
                  .read(healthDataLoaderProvider.notifier)
                  .refreshAllHealthData();
            },
            child: SingleChildScrollView(
              physics: AlwaysScrollableScrollPhysics(),
              child: Padding(
                padding: EdgeInsets.all(MySize.size15),
                child: Column(
                    spacing: MySize.size20,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "${_getGreeting()} ${userName != null ? (userName.contains(' ') ? userName.substring(0, userName.indexOf(' ')) : userName) : "User"} 👋",
                                style: TextStyle(
                                    fontSize: MySize.size24,
                                    color: Theme.of(context)
                                        .textTheme
                                        .bodyLarge
                                        ?.color,
                                    fontWeight: FontWeight.w800),
                              ),
                              Row(
                                spacing: MySize.size5,
                                children: [
                                  SvgPicture.asset(
                                    "assets/svg/new_icons/bpm_icon.svg",
                                    height: MySize.size18,
                                    width: MySize.size18,
                                  ),
                                  Text(
                                    "${latestReading?['pulse_rate']?.toString() ?? heartRate.toString()} bpm",
                                    style: TextStyle(
                                      color: Theme.of(context)
                                          .textTheme
                                          .bodyLarge
                                          ?.color,
                                      fontSize: MySize.size14,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  SvgPicture.asset(
                                    "assets/svg/new_icons/health_score_icon.svg",
                                    height: MySize.size18,
                                    width: MySize.size18,
                                  ),
                                  Text(
                                    "60%",
                                    style: TextStyle(
                                      color: Theme.of(context)
                                          .textTheme
                                          .bodyLarge
                                          ?.color,
                                      fontSize: MySize.size14,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                          Container(
                            width: MySize.size40,
                            height: MySize.size40,
                            decoration: BoxDecoration(
                              boxShadow: [
                                BoxShadow(
                                    color: Colors.black.withAlpha(90),
                                    spreadRadius: 1,
                                    blurRadius: 3,
                                    offset: const Offset(0, 2))
                              ],
                              borderRadius: Shape.circular(MySize.size14),
                              color: AppColors.primaryColor,
                            ),
                            child: InkWell(
                              onTap: () {},
                              child: Padding(
                                padding: EdgeInsets.all(MySize.size8),
                                child: SvgPicture.asset(
                                  "assets/svg/new_icons/notification_icon.svg",
                                  height: MySize.size24,
                                  width: MySize.size24,
                                ),
                              ),
                            ),
                          )
                        ],
                      ),

                      healthScoreWidget(),
                      homeCards(),
                      Text(
                        "Health Metrics",
                        style: TextStyle(
                            color: Theme.of(context).textTheme.bodyLarge!.color,
                            fontSize: MySize.size20,
                            fontWeight: FontWeight.bold),
                      ),
                      bloodPressureWidget(),
                      heartRateWidget(),
                      sleepWidget(),
                      activityWidget(),
                      bmiWidget(),
                      menstruationWidget(),
                      // diabetesWidget(latestSugarReading, estimatedHba1c),
                      // kidneyWidget(),
                      //thyroidWidget(),
                      // liverWidget(),
                      // vitaminsWidget(),
                    ]),
              ),
            ),
          )),
    );
  }

  Widget healthScoreWidget() {
    return Container(
      padding: EdgeInsets.all(MySize.size15),
      decoration: BoxDecoration(
          borderRadius: Shape.circular(MySize.size10),
          color: AppColors.primaryColor),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        spacing: MySize.size10,
        children: [
          Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "Your Health Score",
                        style: TextStyle(
                            color: AppColors.white,
                            fontSize: MySize.size18,
                            fontWeight: FontWeight.bold),
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Text(
                            "84",
                            style: TextStyle(
                                color: AppColors.white,
                                fontSize: MySize.size27,
                                fontWeight: FontWeight.bold),
                          ),
                          Text(
                            "/100",
                            style: TextStyle(
                                color: AppColors.white,
                                fontSize: MySize.size18,
                                fontWeight: FontWeight.bold),
                          ),
                        ],
                      ),
                      Row(
                        spacing: MySize.size5,
                        children: [
                          Text(
                            "Good",
                            style: TextStyle(
                                color: AppColors.white,
                                fontSize: MySize.size14,
                                fontWeight: FontWeight.bold),
                          ),
                          Icon(
                            Icons.circle,
                            color: AppColors.lightGrey,
                            size: MySize.size14,
                          ),
                          Text(
                            "Improving",
                            style: TextStyle(
                                color: AppColors.white,
                                fontSize: MySize.size14,
                                fontWeight: FontWeight.bold),
                          ),
                        ],
                      )
                    ],
                  ),
                  SvgPicture.asset(
                    "assets/svg/new_icons/health_card_icon.svg",
                    height: MySize.size90,
                    width: MySize.size90,
                  ),
                ],
              ),
              Padding(
                padding: EdgeInsets.only(
                    top: MySize.size15,
                    bottom: MySize.size15,
                    left: MySize.size5,
                    right: MySize.size5),
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: Shape.circular(MySize.size10),
                    color: AppColors.white.withAlpha(50),
                  ),
                  child: Column(
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              "Complete Your Daily checkin",
                              style: TextStyle(
                                  fontSize: MySize.size15,
                                  color: AppColors.white,
                                  fontWeight: FontWeight.bold),
                            ),
                            SizedBox(
                              child: ElevatedButton(
                                  onPressed: () {},
                                  child: Text(
                                    "Check in",
                                    style: TextStyle(
                                        fontSize: MySize.size14,
                                        fontWeight: FontWeight.bold,
                                        color: AppColors.black),
                                  )),
                            )
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              )
            ],
          ),
        ],
      ),
    );
  }

  Widget activityWidget() {
    final healthData = ref.watch(healthDataProvider);
    final healthNotifier = ref.watch(healthDataProvider.notifier);

    // Get goals
    final stepGoal = healthNotifier.getDailyStepGoal();
    final distanceGoal = healthNotifier.getDailyDistanceGoal();

    return InkWell(
      onTap: () {
        Navigator.pushNamed(context, healthDetailScreen);
      },
      child: Container(
        padding: EdgeInsets.all(MySize.size15),
        decoration: BoxDecoration(
          borderRadius: Shape.circular(MySize.size10),
          color: Theme.of(context).scaffoldBackgroundColor,
          boxShadow: [
            BoxShadow(
              color: Theme.of(context)
                  .textTheme
                  .bodyLarge!
                  .color!
                  .withValues(alpha: 0.1),
              blurRadius: 5,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with icon and title
            Row(
              children: [
                Padding(
                    padding: EdgeInsets.all(MySize.size4),
                    child: SvgPicture.asset(
                      "assets/svg/notes_icon.svg",

                      height: MySize.size26,
                      width: MySize.size26,
                    ),
                  ),
                Space.width(8),
                Text(
                  "Activity",
                  style: TextStyle(
                    fontSize: MySize.size18,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).textTheme.bodyLarge!.color,
                  ),
                ),
              ],
            ),
            Space.height(20),
            Row(
              children: [
                Expanded(
                  child: _buildActivityCard(
                    iconAsset: "assets/svg/distance_icon.svg",
                    title: "Distance",
                    value:
                        "${healthNotifier.getFormattedDistance()}/$distanceGoal Kms",
                  ),
                ),
                Space.width(10),
                Expanded(
                  child: _buildActivityCard(
                    iconAsset: "assets/svg/calories_icon.svg",
                    title: "Calories",
                    value: "${healthData.calories?.toInt() ?? 0}",
                  ),
                ),
                Space.width(10),
                Expanded(
                  child: _buildActivityCard(
                    iconAsset: "assets/svg/steps_icon.svg",
                    title: "Steps",
                    value: "${healthData.steps ?? 0}/$stepGoal Steps",
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActivityCard({
    required String iconAsset,
    required String title,
    required String value,
  }) {
    return Column(
      children: [
        Container(
          width: MySize.size50,
          height: MySize.size50,
          decoration: BoxDecoration(
            color: AppColors.primaryColor,
            borderRadius: Shape.circular(MySize.size12),
          ),
          child: Padding(
            padding: EdgeInsets.all(MySize.size12),
            child: SvgPicture.asset(
              colorFilter: ColorFilter.mode(
                AppColors.white,
                BlendMode.srcIn,
              ),
              iconAsset,
              height: MySize.size24,
              width: MySize.size24,
            ),
          ),
        ),
        Space.height(8),
        Text(
          title,
          style: TextStyle(
            fontSize: MySize.size16,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).textTheme.bodyLarge!.color,
          ),
        ),
        Space.height(4),
        Text(
          value,
          style: TextStyle(
            fontSize: MySize.size12,
            color: Theme.of(context).textTheme.bodySmall!.color,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget bmiWidget() {
    final bmi = ref.watch(bmiProvider);
    final height = ref.watch(heightProvider);
    final weight = ref.watch(weightProvider);

    final bmiProgress = (bmi / 40.0).clamp(0.0, 1.0);

    return InkWell(
      onTap: () {
        Navigator.pushNamed(context, bmiScreen);
      },
      child: Container(
        padding: EdgeInsets.all(MySize.size15),
        decoration: BoxDecoration(
          borderRadius: Shape.circular(MySize.size10),
          color: Theme.of(context).scaffoldBackgroundColor,
          boxShadow: [
            BoxShadow(
              color: Theme.of(context)
                  .textTheme
                  .bodyLarge!
                  .color!
                  .withValues(alpha: 0.1),
              blurRadius: 5,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "BMI",
                  style: TextStyle(
                    fontSize: MySize.size18,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).textTheme.bodyLarge!.color,
                  ),
                ),
                Text(
                  "Your BMI",
                  style: TextStyle(
                    fontSize: MySize.size14,
                    color: Theme.of(context).textTheme.bodySmall!.color,
                  ),
                ),
              ],
            ),
            Space.height(5),
            // BMI Circle
            Center(
              child: SizedBox(
                width: MySize.size120,
                height: MySize.size120,
                child: CircularPercentIndicator(
                  radius: MySize.size60,
                  lineWidth: MySize.size8,
                  percent: bmiProgress,
                  center: Text(
                    bmi > 0 ? bmi.toStringAsFixed(1) : "0.0",
                    style: TextStyle(
                      fontSize: MySize.size24,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).textTheme.bodyLarge!.color,
                    ),
                  ),
                  backgroundColor: AppColors.progressBackground.withAlpha(31),
                  progressColor: AppColors.primaryColor,
                  circularStrokeCap: CircularStrokeCap.round,
                ),
              ),
            ),
            Space.height(20),
            // Height and Weight Cards
            Row(
              children: [
                Expanded(
                  child: _buildBMICard(
                    iconAsset: "assets/svg/height_icon.svg",
                    title: "Height",
                    value: height > 0 ? height.toStringAsFixed(0) : "00",
                    unit: "Cm",
                  ),
                ),
                Space.width(15),
                Expanded(
                  child: _buildBMICard(
                    iconAsset: "assets/svg/weight_icon.svg",
                    title: "Weight",
                    value: weight > 0 ? weight.toStringAsFixed(0) : "00",
                    unit: "Kgs",
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBMICard({
    required String iconAsset,
    required String title,
    required String value,
    required String unit,
  }) {
    return Card(
      color: Theme.of(context).scaffoldBackgroundColor,
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Padding(
                    padding: EdgeInsets.all(MySize.size4),
                    child: SvgPicture.asset(
                      colorFilter: ColorFilter.mode(
                        AppColors.primaryColor,
                        BlendMode.srcIn,
                      ),
                      iconAsset,
                      height: MySize.size24,
                      width: MySize.size24,
                    ),
                  ),
                Space.width(8),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: MySize.size14,
                    color: Theme.of(context).textTheme.bodyLarge!.color,
                  ),
                ),
              ],
            ),
            Space.height(8),
            Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  value,
                  style: TextStyle(
                    fontSize: MySize.size24,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).textTheme.bodyLarge!.color,
                  ),
                ),
                Space.width(8),
                Padding(
                  padding: EdgeInsets.only(bottom: MySize.size4),
                  child: Text(
                    unit,
                    style: TextStyle(
                      fontSize: MySize.size14,
                      color: Theme.of(context).textTheme.bodySmall!.color,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget sleepWidget() {
    final healthData = ref.watch(healthDataProvider);

    // Calculate sleep quality percentage (assuming 8 hours is 100%)
    final sleepHours = healthData.sleepHours ?? 0;
    final sleepQualityPercent = (sleepHours / 8.0).clamp(0.0, 1.0);
    final sleepQualityDisplay = (sleepQualityPercent * 100).toInt();

    // Format sleep hours and minutes
    final sleepHoursInt = sleepHours.floor();
    final sleepMinutes = ((sleepHours - sleepHoursInt) * 60).round();

    return InkWell(
      onTap: () => Navigator.pushNamed(context, sleepAnalysisScreen),
      child: Container(
        padding: EdgeInsets.only(
          left: MySize.size15,
          right: MySize.size15,
          top: MySize.size15,
          bottom: MySize.size10,
        ),
        decoration: BoxDecoration(
            boxShadow: [
              BoxShadow(
                color: Theme.of(context)
                    .textTheme
                    .bodyLarge!
                    .color!
                    .withValues(alpha: 0.1),
                blurRadius: 5,
                offset: const Offset(0, 2),
              ),
            ],
            borderRadius: Shape.circular(MySize.size10),
            color: Theme.of(context).scaffoldBackgroundColor),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          spacing: MySize.size10,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    SvgPicture.asset(
                      "assets/svg/new_icons/sleep_icon.svg",
                      height: MySize.size26,
                      width: MySize.size26,
                    ),
                    Space.width(10),
                    Text(
                      "Sleep Analysis",
                      style: TextStyle(
                          fontSize: MySize.size18, fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
                Row(
                  spacing: MySize.size5,
                  children: [
                    Text(
                      "Today",
                      style: TextStyle(
                          fontSize: MySize.size16,
                          fontWeight: FontWeight.w500,
                          color: Theme.of(context).textTheme.bodySmall!.color),
                    ),
                    Icon(
                      Icons.arrow_forward_ios,
                      size: MySize.size16,
                      color: Theme.of(context).textTheme.bodyLarge!.color,
                    )
                  ],
                ),
              ],
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "${sleepHours.floor()}h ${sleepMinutes}m",
                          style: TextStyle(
                              fontSize: MySize.size26,
                              fontWeight: FontWeight.w600,
                              color:
                                  Theme.of(context).textTheme.bodyLarge!.color),
                        ),
                        Text(
                          "Sleep Quality",
                          style: TextStyle(
                              fontSize: MySize.size16,
                              fontWeight: FontWeight.w500,
                              color:
                                  Theme.of(context).textTheme.bodySmall!.color),
                        ),
                      ],
                    ),
                  ],
                ),
                Space.width(20),
                CircularPercentIndicator(
                  radius: MySize.size50,
                  lineWidth: MySize.size10,
                  percent: sleepQualityPercent,
                  center: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text("$sleepQualityDisplay%",
                            style: TextStyle(
                                fontSize: MySize.size23,
                                fontWeight: FontWeight.w600)),
                      ],
                    ),
                  ),
                  backgroundColor: AppColors.progressBackground.withAlpha(31),
                  progressColor: AppColors.blue,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget bloodPressureWidget() {
    final latestReading = ref.watch(latestBloodPressureReadingProvider);
    final systolic = latestReading?['systolic']?.toString() ?? "00";
    final diastolic = latestReading?['diastolic']?.toString() ?? "00";

    return InkWell(
      onTap: () => Navigator.pushNamed(context, bloodPressureScreen),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(MySize.size15),
        decoration: BoxDecoration(
          borderRadius: Shape.circular(MySize.size10),
          boxShadow: [
            BoxShadow(
              color: Theme.of(context)
                  .textTheme
                  .bodyLarge!
                  .color!
                  .withValues(alpha: 0.1),
              blurRadius: 5,
              offset: const Offset(0, 2),
            ),
          ],
          color: Theme.of(context).scaffoldBackgroundColor,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  spacing: MySize.size10,
                  children: [
                    SvgPicture.asset(
                      "assets/svg/new_icons/blood_pressure_icon.svg",
                      height: MySize.size26,
                      width: MySize.size26,
                    ),
                    Text(
                      "Blood Pressure",
                      style: TextStyle(
                        fontSize: MySize.size18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                Row(
                  spacing: MySize.size5,
                  children: [
                    Text(
                      "Today",
                      style: TextStyle(
                          fontSize: MySize.size16,
                          fontWeight: FontWeight.w500,
                          color: Theme.of(context).textTheme.bodySmall!.color),
                    ),
                    Icon(
                      Icons.arrow_forward_ios,
                      size: MySize.size16,
                      color: Theme.of(context).textTheme.bodyLarge!.color,
                    )
                  ],
                ),
              ],
            ),
            Space.height(10),
            Row(
              children: [
                Text(
                  systolic,
                  style: TextStyle(
                      fontSize: MySize.size26,
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).textTheme.bodyLarge!.color),
                ),
                Text(
                  "/",
                  style: TextStyle(
                      fontSize: MySize.size26,
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).textTheme.bodyLarge!.color),
                ),
                Text(
                  diastolic,
                  style: TextStyle(
                      fontSize: MySize.size26,
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).textTheme.bodyLarge!.color),
                ),
                Space.width(5),
                Text(
                  "mmHg",
                  style: TextStyle(
                      fontSize: MySize.size16,
                      fontWeight: FontWeight.w500,
                      color: Theme.of(context).textTheme.bodyLarge!.color),
                ),
              ],
            ),
            Text(
              "Stable Range",
              style: TextStyle(
                  fontSize: MySize.size16,
                  fontWeight: FontWeight.w500,
                  color: Theme.of(context).textTheme.bodySmall!.color),
            ),
          ],
        ),
      ),
    );
  }

  Widget heartRateWidget() {
    final latestReading = ref.watch(latestBloodPressureReadingProvider);
    final heartRate = ref.watch(healthDataProvider).heartRate?.toInt() ?? 00;
    final pulseRate =
        latestReading?['pulse_rate']?.toString() ?? heartRate.toString();

    return InkWell(
      onTap: () => Navigator.pushNamed(context, bloodPressureScreen),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(MySize.size15),
        decoration: BoxDecoration(
          borderRadius: Shape.circular(MySize.size10),
          boxShadow: [
            BoxShadow(
              color: Theme.of(context)
                  .textTheme
                  .bodyLarge!
                  .color!
                  .withValues(alpha: 0.1),
              blurRadius: 5,
              offset: const Offset(0, 2),
            ),
          ],
          color: Theme.of(context).scaffoldBackgroundColor,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  spacing: MySize.size10,
                  children: [
                    SvgPicture.asset(
                      "assets/svg/new_icons/heart_rate_icon.svg",
                      height: MySize.size26,
                      width: MySize.size26,
                    ),
                    Text(
                      "Heart Rate",
                      style: TextStyle(
                        fontSize: MySize.size18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                Row(
                  spacing: MySize.size5,
                  children: [
                    Text(
                      "Today",
                      style: TextStyle(
                          fontSize: MySize.size16,
                          fontWeight: FontWeight.w500,
                          color: Theme.of(context).textTheme.bodySmall!.color),
                    ),
                    Icon(
                      Icons.arrow_forward_ios,
                      size: MySize.size16,
                      color: Theme.of(context).textTheme.bodyLarge!.color,
                    )
                  ],
                ),
              ],
            ),
            Space.height(10),
            Row(
              children: [
                Text(
                  pulseRate,
                  style: TextStyle(
                      fontSize: MySize.size26,
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).textTheme.bodyLarge!.color),
                ),
                Space.width(5),
                Text(
                  "bpm",
                  style: TextStyle(
                      fontSize: MySize.size16,
                      fontWeight: FontWeight.w500,
                      color: Theme.of(context).textTheme.bodyLarge!.color),
                ),
              ],
            ),
            Text(
              "Resting Rate",
              style: TextStyle(
                  fontSize: MySize.size16,
                  fontWeight: FontWeight.w500,
                  color: Theme.of(context).textTheme.bodySmall!.color),
            ),
          ],
        ),
      ),
    );
  }

  Widget diabetesWidget(latestSugarReading, estimatedHba1c) {
    // Get the latest HbA1c reading
    final latestValue = ref.watch(latestHba1cReadingProvider);
    final latestDate = ref.watch(latestHba1cDateProvider);

    // Format HbA1c value with one decimal place
    final hba1cDisplay =
        latestValue != null ? "${latestValue.toStringAsFixed(1)}%" : "N/A";

    // Format the date text
    String dateText = "Not Measured Yet";
    if (latestDate != null) {
      try {
        final parts = latestDate.split('-');
        if (parts.length == 3) {
          final day = int.parse(parts[0]);
          final month = int.parse(parts[1]);
          final year = int.parse(parts[2]);
          final date = DateTime(year, month, day);
          final now = DateTime.now();
          final difference = now.difference(date);

          if (difference.inDays < 1) {
            dateText = "Recent HbA1c\nToday";
          } else if (difference.inDays == 1) {
            dateText = "Recent HbA1c\nYesterday";
          } else if (difference.inDays < 30) {
            dateText = "Recent HbA1c\n${difference.inDays} days ago";
          } else if (difference.inDays < 60) {
            dateText = "Recent HbA1c\n1 month ago";
          } else {
            final months = (difference.inDays / 30).floor();
            dateText = "Recent HbA1c\n$months months ago";
          }
        }
      } catch (e) {
        dateText = "Not Measured Yet";
      }
    }

    return Container(
      padding: EdgeInsets.all(MySize.size15),
      decoration: BoxDecoration(
          borderRadius: Shape.circular(MySize.size10),
          color: Theme.of(context).cardColor),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        spacing: MySize.size10,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: EdgeInsets.all(MySize.size15),
                decoration: BoxDecoration(
                  borderRadius: Shape.circular(MySize.size10),
                  color: Theme.of(context).cardColor,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "Diabetes Management",
                      style: TextStyle(
                        fontSize: MySize.size18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
              Space.height(10),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    flex: 1,
                    child: InkWell(
                      onTap: () {
                        Navigator.pushNamed(context, '/hba1cScreen');
                      },
                      child: Container(
                        height: MySize.size180,
                        padding: EdgeInsets.all(MySize.size10),
                        decoration: BoxDecoration(
                          color: AppColors.primaryColor,
                          borderRadius: Shape.circular(MySize.size10),
                        ),
                        child: Stack(
                          children: [
                            Positioned(
                              top: 0,
                              left: 0,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Container(
                                    width: MySize.size40,
                                    height: MySize.size40,
                                    decoration: BoxDecoration(
                                      color: AppColors.white,
                                      shape: BoxShape.circle,
                                    ),
                                    child: Center(
                                      child: SvgPicture.asset(
                                        'assets/svg/diabetes_icon.svg',
                                        width: MySize.size24,
                                        height: MySize.size24,
                                      ),
                                    ),
                                  ),
                                  Space.height(10),
                                  Text(
                                    dateText,
                                    style: TextStyle(color: AppColors.white),
                                  ),
                                ],
                              ),
                            ),
                            Positioned(
                              top: 0,
                              right: 0,
                              child: Text(
                                hba1cDisplay,
                                style: TextStyle(
                                  color: AppColors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            Positioned(
                              bottom: 0,
                              right: 0,
                              child: Text(
                                latestValue != null
                                    ? latestValue < 5.7
                                        ? "Normal"
                                        : latestValue < 6.5
                                            ? "Pre-Diabetes"
                                            : "Diabetes"
                                    : "N/A",
                                style: TextStyle(
                                  color: AppColors.white,
                                  fontSize: MySize.size12,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  Space.width(10),
                  Expanded(
                    flex: 1,
                    child: Column(
                      children: [
                        Container(
                          height: MySize.size90,
                          width: double.infinity,
                          padding: EdgeInsets.all(MySize.size8),
                          decoration: BoxDecoration(
                            color: Theme.of(context).scaffoldBackgroundColor,
                            borderRadius: Shape.circular(MySize.size10),
                          ),
                          child: Stack(
                            children: [
                              Positioned(
                                top: 0,
                                left: 0,
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Container(
                                      width: MySize.size35,
                                      height: MySize.size35,
                                      decoration: BoxDecoration(
                                        color: AppColors.primaryColor,
                                        shape: BoxShape.circle,
                                      ),
                                      child: Center(
                                        child: SvgPicture.asset(
                                          'assets/svg/diabetes_icon.svg',
                                          width: MySize.size22,
                                          height: MySize.size22,
                                          colorFilter: ColorFilter.mode(
                                              AppColors.white, BlendMode.srcIn),
                                        ),
                                      ),
                                    ),
                                    Space.height(15),
                                    Text(
                                      'Estimated HbA1c',
                                      style: TextStyle(
                                        fontSize: MySize.size12,
                                        color: AppColors.textGray,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Positioned(
                                top: 0,
                                right: 0,
                                child: Text(
                                  "$estimatedHba1c%",
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: MySize.size20,
                                    color: Theme.of(context)
                                        .textTheme
                                        .bodyMedium!
                                        .color,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        Space.height(10),
                        InkWell(
                          onTap: () {
                            Navigator.pushNamed(context, '/diabetesScreen');
                          },
                          child: Container(
                            height: MySize.size90,
                            width: double.infinity,
                            padding: EdgeInsets.all(MySize.size8),
                            decoration: BoxDecoration(
                              color: Theme.of(context).scaffoldBackgroundColor,
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: Stack(
                              children: [
                                Positioned(
                                  top: 0,
                                  left: 0,
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Container(
                                        width: MySize.size35,
                                        height: MySize.size35,
                                        decoration: BoxDecoration(
                                          color: AppColors.primaryColor,
                                          shape: BoxShape.circle,
                                        ),
                                        child: Center(
                                          child: SvgPicture.asset(
                                            'assets/svg/blood_sugar_icon.svg',
                                            width: MySize.size22,
                                            height: MySize.size22,
                                            colorFilter: ColorFilter.mode(
                                                AppColors.white,
                                                BlendMode.srcIn),
                                          ),
                                        ),
                                      ),
                                      Space.height(15),
                                      Text(
                                        'Blood Sugar',
                                        style: TextStyle(
                                          fontSize: MySize.size12,
                                          color: AppColors.textGray,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                Positioned(
                                  top: 0,
                                  right: 0,
                                  child: Column(
                                    children: [
                                      Text(
                                        latestSugarReading.toString(),
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: MySize.size20,
                                          color: Theme.of(context)
                                              .textTheme
                                              .bodyMedium!
                                              .color,
                                        ),
                                      ),
                                      Text(
                                        'mg/dL',
                                        style: TextStyle(
                                          fontSize: MySize.size12,
                                          color: AppColors.primaryColor,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              )
            ],
          )
        ],
      ),
    );
  }

  Widget kidneyWidget() {
    // Get the latest kidney readings from the provider
    final latestReadings = ref.watch(latestKidneyReadingsProvider);

    // Extract values or use defaults if no readings exist
    final gfr = latestReadings?['gfr']?.toString() ?? "N/A";
    final creatinine = latestReadings?['creatinine']?.toString() ?? "N/A";
    final bun = latestReadings?['bun']?.toString() ?? "N/A";
    final albumin = latestReadings?['albumin']?.toString() ?? "N/A";

    return InkWell(
      onTap: () => Navigator.pushNamed(context, kidneyManagementScreen),
      child: Container(
        padding: EdgeInsets.all(MySize.size15),
        decoration: BoxDecoration(
            borderRadius: Shape.circular(MySize.size10),
            color: Theme.of(context).cardColor),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          spacing: MySize.size10,
          children: [
            Text(
              "Kidney Management",
              style: TextStyle(
                  fontSize: MySize.size18, fontWeight: FontWeight.bold),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              spacing: MySize.size5,
              children: [
                KidneyCard(
                  title: "GFR Rate",
                  value: gfr,
                  icon: "assets/svg/gfr_icon.svg",
                  unit: "U/L",
                ),
                KidneyCard(
                  title: "Creatinine",
                  value: creatinine,
                  icon: "assets/svg/creatinine_icon.svg",
                  unit: "mg/dl",
                ),
              ],
            ),
            Row(
              spacing: MySize.size5,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                KidneyCard(
                  title: "BUN",
                  value: bun,
                  icon: "assets/svg/bun_icon.svg",
                  unit: "mg/dl",
                ),
                KidneyCard(
                  title: "Albumin",
                  value: albumin,
                  icon: "assets/svg/albumin_icon.svg",
                  unit: "U/L",
                ),
              ],
            )
          ],
        ),
      ),
    );
  }

  Widget thyroidWidget() {
    // Get the latest thyroid reading from the provider
    final latestReading = ref.watch(latestThyroidReadingProvider);
    final thyroidStatus = ref.watch(thyroidStatusProvider);

    // Extract values or use defaults if no readings exist
    final tsh = latestReading?['tsh']?.toString() ?? "N/A";
    final t3 = latestReading?['t3']?.toString() ?? "N/A";
    final t4 = latestReading?['t4']?.toString() ?? "N/A";

    return InkWell(
      onTap: () => Navigator.pushNamed(context, thyroidManagementScreen),
      child: Container(
        padding: EdgeInsets.all(MySize.size15),
        decoration: BoxDecoration(
            borderRadius: Shape.circular(MySize.size10),
            color: Theme.of(context).cardColor),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    "Thyroid Management",
                    style: TextStyle(
                        fontSize: MySize.size18, fontWeight: FontWeight.bold),
                  ),
                ),
                if (thyroidStatus != "Unknown")
                  Text(
                    thyroidStatus,
                    style: TextStyle(
                      fontSize: MySize.size12,
                      fontWeight: FontWeight.bold,
                      color: thyroidStatus == "Normal"
                          ? AppColors.primaryColor
                          : AppColors.red,
                    ),
                  ),
              ],
            ),
            SizedBox(height: MySize.size10),
            Row(
              children: [
                Expanded(
                  child: ThyroidCard(
                    title: "TSH",
                    value: tsh,
                    icon: "assets/svg/tsh_icon.svg",
                    unit: "mIU/L",
                  ),
                ),
                SizedBox(width: MySize.size8),
                Expanded(
                  child: ThyroidCard(
                    title: "T3",
                    value: t3,
                    icon: "assets/svg/freet3_icon.svg",
                    unit: "pg/mL",
                  ),
                ),
                SizedBox(width: MySize.size8),
                Expanded(
                  child: ThyroidCard(
                    title: "T4",
                    value: t4,
                    icon: "assets/svg/freet4_icon.svg",
                    unit: "ng/dL",
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget liverWidget() {
    // Get the latest liver readings from the provider
    final latestReading = ref.watch(latestLiverReadingProvider);
    final liverStatus = ref.watch(liverHealthStatusProvider);

    // Extract values or use defaults if no readings exist
    final ast = latestReading?['ast']?.toString() ?? "N/A";
    final alt = latestReading?['alt']?.toString() ?? "N/A";
    final bilirubin = latestReading?['bilirubin']?.toString() ?? "N/A";
    final alp = latestReading?['alp']?.toString() ?? "N/A";

    return InkWell(
      onTap: () {
        Navigator.pushNamed(context, liverScreen);
      },
      child: Container(
        padding: EdgeInsets.all(MySize.size15),
        decoration: BoxDecoration(
            borderRadius: Shape.circular(MySize.size10),
            color: Theme.of(context).cardColor),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          spacing: MySize.size10,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "Liver Management",
                  style: TextStyle(
                      fontSize: MySize.size18, fontWeight: FontWeight.bold),
                ),
                if (liverStatus != "Unknown")
                  Text(
                    liverStatus,
                    style: TextStyle(
                      fontSize: MySize.size12,
                      fontWeight: FontWeight.bold,
                      color: liverStatus == "Normal"
                          ? AppColors.primaryColor
                          : AppColors.red,
                    ),
                  ),
              ],
            ),
            GridView.count(
              shrinkWrap: true,
              physics: NeverScrollableScrollPhysics(),
              mainAxisSpacing: MySize.size5,
              crossAxisSpacing: MySize.size5,
              crossAxisCount: 2,
              children: [
                LiverCards(
                    title: 'Aspartate Aminotransferase',
                    value: ast,
                    icon: 'assets/svg/ast_icon.svg',
                    unit: 'U/L'),
                LiverCards(
                    title: 'Alanine Aminotransferase',
                    value: alt,
                    icon: 'assets/svg/ame_icon.svg',
                    unit: 'U/L'),
                LiverCards(
                    title: 'Bilirubin Levels',
                    value: bilirubin,
                    icon: 'assets/svg/bilirubin_icon.svg',
                    unit: 'mg/dL'),
                LiverCards(
                    title: 'Alkaline Phosphatase',
                    value: alp,
                    icon: 'assets/svg/alkaline_icon.svg',
                    unit: 'U/L'),
              ],
            )
          ],
        ),
      ),
    );
  }

  Widget menstruationWidget() {
    // Watch period data from providers
    final periodData = ref.watch(periodProvider);
    final periodPhase = ref.watch(periodPhaseProvider);
    final nextPeriod = ref.watch(nextPeriodProvider);

    // Extract cycle length and period duration from the latest data
    int cycleLength = 28; // default cycle length
    int periodDuration = 5; // default period duration
    String cycleStatus = "Normal";
    String periodStatus = "Normal";

    // Get data from period history
    if (periodData.isNotEmpty) {
      // Get the latest period data
      final sortedKeys = periodData.keys.toList()..sort((a, b) {
        final dateA = DateFormat('d-M-yyyy').parse(a);
        final dateB = DateFormat('d-M-yyyy').parse(b);
        return dateB.compareTo(dateA); // Most recent first
      });

      if (sortedKeys.isNotEmpty) {
        final latestData = periodData[sortedKeys.first];
        if (latestData != null && latestData['readings'] != null) {
          final readings = latestData['readings'] as List;
          if (readings.isNotEmpty) {
            final reading = readings[0];
            cycleLength = reading['cycle_length'] ?? 28;
            periodDuration = reading['period_duration'] ?? 5;
          }
        }
      }
    }

    // Use next period data if available (more accurate)
    if (nextPeriod?.cycleLength != null) {
      cycleLength = nextPeriod!.cycleLength!;
    }

    // Determine cycle status based on cycle length
    if (cycleLength < 21 || cycleLength > 35) {
      cycleStatus = "Irregular";
    }

    // Determine period status based on period duration
    if (periodDuration < 3 || periodDuration > 7) {
      periodStatus = "Irregular";
    }

    // Get current cycle day
    int currentCycleDay = periodPhase?.cycleDay ?? 0;

    // For Period Status card, show current cycle day if available, otherwise period duration
    int displayValue = currentCycleDay > 0 ? currentCycleDay : periodDuration;
    String statusText = currentCycleDay > 0 ? "Day $currentCycleDay" : periodStatus;

    return InkWell(
      onTap: () {
        Navigator.pushNamed(context, periodTrackerScreen);
      },
      child: Container(
        padding: EdgeInsets.all(MySize.size10),
        decoration: BoxDecoration(
          borderRadius: Shape.circular(MySize.size10),
          color: Theme.of(context).scaffoldBackgroundColor,
          boxShadow: [
            BoxShadow(
              color: Theme.of(context)
                  .textTheme
                  .bodyLarge!
                  .color!
                  .withValues(alpha: 0.1),
              blurRadius: 5,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Text(
              "Menstrual Cycle",
              style: TextStyle(
                fontSize: MySize.size18,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).textTheme.bodyLarge!.color,
              ),
            ),
            Space.height(20),
            // Period Length and Period Status Cards
            Row(
              children: [
                Expanded(
                  child: _buildMenstrualCard(
                    iconAsset: "assets/svg/period_length_icon.svg",
                    title: "Period Length",
                    value: cycleLength.toString(),
                    unit: "Days",
                    status: cycleStatus,
                    statusColor: cycleStatus == "Normal" ? AppColors.primaryColor : Colors.orange,
                  ),
                ),
                Space.width(15),
                Expanded(
                  child: _buildMenstrualCard(
                    iconAsset: "assets/svg/period_status_icon.svg",
                    title: "Period Status",
                    value: displayValue.toString(),
                    unit: "Days",
                    status: statusText,
                    statusColor: periodStatus == "Normal" ? AppColors.primaryColor : Colors.orange,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMenstrualCard({
    required String iconAsset,
    required String title,
    required String value,
    required String unit,
    required String status,
    required Color statusColor,
  }) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(MySize.size8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Icon and title row
            Row(
              children: [
                Container(
                  width: MySize.size40,
                  height: MySize.size40,
                  decoration: BoxDecoration(
                    color: AppColors.primaryColor,
                    borderRadius: Shape.circular(MySize.size20),
                  ),
                  child: Padding(
                    padding: EdgeInsets.all(MySize.size8),
                    child: SvgPicture.asset(
                      iconAsset,
                      height: MySize.size24,
                      width: MySize.size24,
                    ),
                  ),
                ),
                Space.width(12),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: MySize.size14,
                    color: Theme.of(context).textTheme.bodySmall!.color,
                  ),
                ),
              ],
            ),
            Space.height(12),
            // Value and unit row
            Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  value,
                  style: TextStyle(
                    fontSize: MySize.size28,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).textTheme.bodyLarge!.color,
                  ),
                ),
                Space.width(8),
                Padding(
                  padding: EdgeInsets.only(bottom: MySize.size6),
                  child: Text(
                    unit,
                    style: TextStyle(
                      fontSize: MySize.size16,
                      fontWeight: FontWeight.w500,
                      color: Theme.of(context).textTheme.bodySmall!.color,
                    ),
                  ),
                ),
              ],
            ),
            Space.height(4),
            // Status
            Text(
              status,
              style: TextStyle(
                fontSize: MySize.size14,
                fontWeight: FontWeight.w500,
                color: statusColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget vitaminsWidget() {
    return Container(
      padding: EdgeInsets.all(MySize.size15),
      decoration: BoxDecoration(
          borderRadius: Shape.circular(MySize.size10),
          color: Theme.of(context).cardColor),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        spacing: MySize.size10,
        children: [
          Row(
            spacing: MySize.size50,
            children: [
              Text(
                "Vitamins and Minerals",
                style: TextStyle(
                    fontSize: MySize.size18, fontWeight: FontWeight.bold),
              ),
            ],
          ),
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: Theme.of(context).scaffoldBackgroundColor,
              borderRadius: Shape.circular(MySize.size10),
            ),
            child: Padding(
              padding: EdgeInsets.only(top: MySize.size8, bottom: MySize.size8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  Column(
                    children: [
                      CircularPercentIndicator(
                        radius: MySize.size30,
                        lineWidth: MySize.size6,
                        percent: 3 / 5,
                        center: Text("3/5"),
                        backgroundColor:
                            AppColors.progressBackground.withValues(alpha: 31),
                        progressColor: AppColors.primaryColor,
                      ),
                      Text(
                        "Vitamins Taken",
                        style: TextStyle(
                            fontWeight: FontWeight.w500,
                            fontSize: MySize.size12),
                      ),
                    ],
                  ),
                  Column(
                    children: [
                      Container(
                        padding: EdgeInsets.all(MySize.size8),
                        width: MySize.size70,
                        decoration: BoxDecoration(
                          shape: BoxShape.rectangle,
                          borderRadius: Shape.circular(MySize.size10),
                          color: AppColors.primaryColor,
                        ),
                        child: SvgPicture.asset(
                          'assets/svg/next_schedule_icon.svg',
                          height: MySize.size24,
                          width: MySize.size24,
                        ),
                      ),
                      Space.height(10),
                      Text(
                        "Next Scheduled",
                        style: TextStyle(fontSize: MySize.size12),
                      ),
                      Text(
                        "omega-3 at 8:00 Pm",
                        style: TextStyle(
                            fontSize: MySize.size9, color: Colors.grey),
                      ),
                    ],
                  ),
                  Column(
                    children: [
                      Container(
                        padding: EdgeInsets.all(MySize.size8),
                        width: MySize.size70,
                        decoration: BoxDecoration(
                          shape: BoxShape.rectangle,
                          borderRadius: Shape.circular(MySize.size10),
                          color: AppColors.primaryColor,
                        ),
                        child: SvgPicture.asset(
                          'assets/svg/missed_intake_icon.svg',
                          height: MySize.size24,
                          width: MySize.size24,
                        ),
                      ),
                      Space.height(10),
                      Text(
                        "Missed Intake",
                        style: TextStyle(fontSize: MySize.size12),
                      ),
                      Text(
                        "1 Vitmain Yesterday",
                        style: TextStyle(
                            fontSize: MySize.size9, color: Colors.grey),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          Space.height(10),
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: Theme.of(context).scaffoldBackgroundColor,
              borderRadius: Shape.circular(MySize.size10),
            ),
            child: Padding(
              padding: EdgeInsets.symmetric(vertical: MySize.size10),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  Column(
                    children: [
                      Container(
                        padding: EdgeInsets.all(MySize.size8),
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: AppColors.primaryColor,
                        ),
                        child: SvgPicture.asset(
                          'assets/svg/calcium_icon.svg',
                          height: MySize.size24,
                          width: MySize.size24,
                        ),
                      ),
                      Space.height(6),
                      Text("9.2",
                          style: TextStyle(
                              fontSize: MySize.size18,
                              fontWeight: FontWeight.bold)),
                      Text("mg/DL",
                          style: TextStyle(
                              fontSize: MySize.size10, color: Colors.grey)),
                      Text("Calcium", style: TextStyle(fontSize: 10)),
                    ],
                  ),
                  Column(
                    children: [
                      Container(
                        padding: EdgeInsets.all(MySize.size8),
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: AppColors.primaryColor,
                        ),
                        child: SvgPicture.asset(
                          'assets/svg/magnesium_icon.svg',
                          height: MySize.size24,
                          width: MySize.size24,
                        ),
                      ),
                      Space.height(6),
                      Text("1.6",
                          style: TextStyle(
                              fontSize: MySize.size18,
                              fontWeight: FontWeight.bold)),
                      Text("mg/DL",
                          style: TextStyle(
                              fontSize: MySize.size10, color: Colors.grey)),
                      Text("Magnesium", style: TextStyle(fontSize: 10)),
                    ],
                  ),
                  Column(
                    children: [
                      Container(
                        padding: EdgeInsets.all(MySize.size8),
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: AppColors.primaryColor,
                        ),
                        child: SvgPicture.asset(
                          'assets/svg/phosphorus_icon.svg',
                          height: MySize.size24,
                          width: MySize.size24,
                        ),
                      ),
                      Space.height(6),
                      Text("9.2",
                          style: TextStyle(
                              fontSize: MySize.size18,
                              fontWeight: FontWeight.bold)),
                      Text("mg/DL",
                          style: TextStyle(
                              fontSize: MySize.size10, color: Colors.grey)),
                      Text("Phosphorus",
                          style: TextStyle(fontSize: MySize.size10)),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget homeCards() {
    // Watch providers for dynamic data
    final glassesToday = ref.watch(glassesTodayProvider);
    final glassesPerDay = ref.watch(glassesPerDayProvider);
    final reportState = ref.watch(reportListProvider);

    // Calculate reports count
    final reportsCount = reportState.when(
      data: (reports) => reports.length,
      loading: () => 0,
      error: (_, __) => 0,
    );

    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      mainAxisSpacing: MySize.size15,
      crossAxisSpacing: MySize.size15,
      childAspectRatio: 2.5,
      children: [
        _buildHomeCard(
          icon: Icons.medication,
          title: "Medication",
          subtitle: "Add or Upload",
          onTap: () => Navigator.pushNamed(context, setupMedicationScreen),
        ),
        _buildHomeCard(
          icon: Icons.description,
          title: "Reports",
          subtitle: "$reportsCount Reports",
          onTap: () => Navigator.pushNamed(context, reportScreen),
        ),
        _buildHomeCard(
          icon: Icons.local_drink,
          title: "Water Intake",
          subtitle: "$glassesToday/$glassesPerDay Glasses",
          onTap: () => Navigator.pushNamed(context, waterInTakeScreen),
        ),
        _buildHomeCard(
          icon: Icons.calendar_today,
          title: "Book A Test",
          subtitle: "At Home",
          onTap: () => Navigator.pushNamed(context, bookTestScreen),
        ),
      ],
    );
  }

  Widget _buildHomeCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: Shape.circular(MySize.size12),
      child: Container(
        padding: EdgeInsets.all(MySize.size15),
        decoration: BoxDecoration(
          borderRadius: Shape.circular(MySize.size12),
          color: Theme.of(context).scaffoldBackgroundColor,
          boxShadow: [
            BoxShadow(
              color: Theme.of(context)
                  .textTheme
                  .bodyLarge!
                  .color!
                  .withValues(alpha: 0.1),
              blurRadius: 5,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: MySize.size50,
              height: MySize.size50,
              decoration: BoxDecoration(
                borderRadius: Shape.circular(MySize.size12),
                color: AppColors.primaryColor,
              ),
              child: Icon(
                icon,
                color: AppColors.white,
                size: MySize.size24,
              ),
            ),
            Space.width(12),
            Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: MySize.size14,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).textTheme.bodyLarge!.color,
                  ),
                ),
                Space.height(4),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: MySize.size12,
                    color: Theme.of(context).textTheme.bodySmall!.color,
                  ),
                ),
              ],
            )
          ],
        ),
      ),
    );
  }

  String _getGreeting() {
    final hour = DateTime.now().hour;
    if (hour < 12) {
      return 'Morning';
    } else if (hour < 17) {
      return 'Afternoon';
    } else {
      return 'Evening';
    }
  }
}
