/// FitBit API configuration
/// 
/// To set up FitBit integration:
/// 1. Go to https://dev.fitbit.com/apps/new
/// 2. Create a new FitBit application
/// 3. Set the OAuth 2.0 Application Type to "Personal"
/// 4. Set the Callback URL to: com.example.healo://fitbit/callback
/// 5. Replace the placeholder values below with your actual credentials
/// 
/// Note: Keep these credentials secure and never commit them to version control
/// in a production app. Consider using environment variables or secure storage.

class FitBitConfig {
  // TODO: Replace with your actual FitBit app credentials
  static const String clientId = 'YOUR_FITBIT_CLIENT_ID';
  static const String clientSecret = 'YOUR_FITBIT_CLIENT_SECRET';
  
  // This should match your app's URL scheme
  static const String redirectUri = 'com.example.healo://fitbit/callback';
  
  // FitBit API scopes - these determine what data your app can access
  static const List<String> scopes = [
    'activity',    // Steps, distance, calories, active minutes
    'heartrate',   // Heart rate data
    'location',    // GPS and location data
    'nutrition',   // Food and water logging
    'profile',     // User profile information
    'settings',    // User preferences and settings
    'sleep',       // Sleep data and analysis
    'social',      // Friends and social features
    'weight',      // Weight and body measurements
  ];
  
  // API endpoints
  static const String baseUrl = 'https://api.fitbit.com';
  static const String authUrl = 'https://www.fitbit.com/oauth2/authorize';
  static const String tokenUrl = 'https://api.fitbit.com/oauth2/token';
  
  // Validation
  static bool get isConfigured {
    return clientId != 'YOUR_FITBIT_CLIENT_ID' && 
           clientSecret != 'YOUR_FITBIT_CLIENT_SECRET' &&
           clientId.isNotEmpty && 
           clientSecret.isNotEmpty;
  }
  
  static String get configurationInstructions {
    return '''
To configure FitBit integration:

1. Visit https://dev.fitbit.com/apps/new
2. Create a new FitBit application with these settings:
   - Application Name: Healo Health App
   - Description: Health tracking and insights app
   - Application Website: https://your-app-website.com
   - Organization: Your Organization Name
   - Organization Website: https://your-organization.com
   - Terms of Service URL: https://your-app-website.com/terms
   - Privacy Policy URL: https://your-app-website.com/privacy
   - OAuth 2.0 Application Type: Personal
   - Callback URL: com.example.healo://fitbit/callback
   - Default Access Type: Read & Write

3. After creating the app, copy the Client ID and Client Secret
4. Replace the placeholder values in lib/config/fitbit_config.dart
5. Update the URL scheme in your app configuration files

For iOS (ios/Runner/Info.plist):
<key>CFBundleURLTypes</key>
<array>
  <dict>
    <key>CFBundleURLName</key>
    <string>fitbit-oauth</string>
    <key>CFBundleURLSchemes</key>
    <array>
      <string>com.example.healo</string>
    </array>
  </dict>
</array>

For Android (android/app/src/main/AndroidManifest.xml):
<activity
    android:name=".MainActivity"
    android:exported="true"
    android:launchMode="singleTop"
    android:theme="@style/LaunchTheme">
    <intent-filter android:autoVerify="true">
        <action android:name="android.intent.action.VIEW" />
        <category android:name="android.intent.category.DEFAULT" />
        <category android:name="android.intent.category.BROWSABLE" />
        <data android:scheme="com.example.healo" />
    </intent-filter>
</activity>
''';
  }
}
